using ComicApp.Data;
using ComicApp.DTOs.Quest;
using ComicApp.Models.Quest;
using ComicApp.Reposibility;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;

namespace ComicApp.Reposibility
{
    public class QuestRepository : IQuestRepository
    {
        private readonly ComicDbContext _dbContext;
        private readonly IItemRepository _itemRepository;
        private readonly ILogger<QuestRepository> _logger;

        public QuestRepository(ComicDbContext dbContext, IItemRepository itemRepository, ILogger<QuestRepository> logger)
        {
            _dbContext = dbContext;
            _itemRepository = itemRepository;
            _logger = logger;
        }

        public async Task<List<DailyQuestDTO>> GetDailyQuestsAsync(int userId)
        {
            try
            {
                var today = DateTime.UtcNow.Date;
                var userQuests = await _dbContext.UserQuests
                    .Include(uq => uq.QuestTemplate)
                    .ThenInclude(qt => qt.Rewards)
                    .Where(uq => uq.UserID == userId && 
                                uq.QuestTemplate.IsDaily && 
                                uq.CreateAt.Date == today)
                    .ToListAsync();

                // If no quests for today, create them
                if (!userQuests.Any())
                {
                    await CreateDailyQuestsForUserAsync(userId);
                    userQuests = await _dbContext.UserQuests
                        .Include(uq => uq.QuestTemplate)
                        .ThenInclude(qt => qt.Rewards)
                        .Where(uq => uq.UserID == userId && 
                                    uq.QuestTemplate.IsDaily && 
                                    uq.CreateAt.Date == today)
                        .ToListAsync();
                }

                return userQuests.Select(uq => new DailyQuestDTO
                {
                    Id = uq.ID.ToString(),
                    Title = uq.QuestTemplate.Title,
                    Description = uq.QuestTemplate.Description ?? "",
                    Type = uq.QuestTemplate.QuestType,
                    Target = uq.QuestTemplate.Target,
                    Current = uq.Current,
                    Status = uq.Status,
                    Icon = uq.QuestTemplate.Icon ?? "",
                    Difficulty = uq.QuestTemplate.Difficulty,
                    ExpiresAt = uq.ExpiresAt,
                    CompletedAt = uq.CompletedAt,
                    Reward = new QuestRewardDTO
                    {
                        Type = uq.QuestTemplate.Rewards.FirstOrDefault()?.RewardType ?? "",
                        Amount = uq.QuestTemplate.Rewards.FirstOrDefault()?.Amount ?? 0,
                        Item = uq.QuestTemplate.Rewards.FirstOrDefault()?.Item,
                        Description = uq.QuestTemplate.Rewards.FirstOrDefault()?.Description ?? ""
                    }
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting daily quests for user {UserId}", userId);
                return new List<DailyQuestDTO>();
            }
        }

        public async Task<List<WeeklyQuestDTO>> GetWeeklyQuestsAsync(int userId)
        {
            try
            {
                var currentWeek = GetCurrentWeekNumber();
                var userQuests = await _dbContext.UserQuests
                    .Include(uq => uq.QuestTemplate)
                    .ThenInclude(qt => qt.Rewards)
                    .Where(uq => uq.UserID == userId && 
                                uq.QuestTemplate.IsWeekly && 
                                uq.WeekNumber == currentWeek)
                    .ToListAsync();

                // If no weekly quests, create them
                if (!userQuests.Any())
                {
                    await CreateWeeklyQuestsForUserAsync(userId);
                    userQuests = await _dbContext.UserQuests
                        .Include(uq => uq.QuestTemplate)
                        .ThenInclude(qt => qt.Rewards)
                        .Where(uq => uq.UserID == userId && 
                                    uq.QuestTemplate.IsWeekly && 
                                    uq.WeekNumber == currentWeek)
                        .ToListAsync();
                }

                return userQuests.Select(uq => new WeeklyQuestDTO
                {
                    Id = uq.ID.ToString(),
                    Title = uq.QuestTemplate.Title,
                    Description = uq.QuestTemplate.Description ?? "",
                    Type = uq.QuestTemplate.QuestType,
                    Target = uq.QuestTemplate.Target,
                    Current = uq.Current,
                    Status = uq.Status,
                    Icon = uq.QuestTemplate.Icon ?? "",
                    Difficulty = uq.QuestTemplate.Difficulty,
                    ExpiresAt = uq.ExpiresAt,
                    CompletedAt = uq.CompletedAt,
                    WeekNumber = uq.WeekNumber ?? 0,
                    ResetDay = uq.ResetDay ?? "Monday",
                    Reward = new QuestRewardDTO
                    {
                        Type = uq.QuestTemplate.Rewards.FirstOrDefault()?.RewardType ?? "",
                        Amount = uq.QuestTemplate.Rewards.FirstOrDefault()?.Amount ?? 0,
                        Item = uq.QuestTemplate.Rewards.FirstOrDefault()?.Item,
                        Description = uq.QuestTemplate.Rewards.FirstOrDefault()?.Description ?? ""
                    }
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting weekly quests for user {UserId}", userId);
                return new List<WeeklyQuestDTO>();
            }
        }

        public async Task<UserQuestStatsDTO?> GetUserQuestStatsAsync(int userId)
        {
            try
            {
                var stats = await _dbContext.UserQuestStats
                    .FirstOrDefaultAsync(s => s.UserID == userId);

                if (stats == null)
                {
                    await InitializeUserQuestStatsAsync(userId);
                    stats = await _dbContext.UserQuestStats
                        .FirstOrDefaultAsync(s => s.UserID == userId);
                }

                return stats == null ? null : new UserQuestStatsDTO
                {
                    TotalCompleted = stats.TotalCompleted,
                    StreakDays = stats.StreakDays,
                    WeeklyCompleted = stats.WeeklyCompleted,
                    MonthlyCompleted = stats.MonthlyCompleted,
                    TotalRewardsEarned = stats.TotalRewardsEarned,
                    Level = stats.Level,
                    Experience = stats.Experience,
                    NextLevelExp = stats.NextLevelExp
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting quest stats for user {UserId}", userId);
                return null;
            }
        }

        public async Task<bool> CreateDailyQuestsForUserAsync(int userId)
        {
            try
            {
                var dailyTemplates = await GetActiveQuestTemplatesAsync(isDaily: true);
                var today = DateTime.UtcNow.Date;
                var tomorrow = today.AddDays(1);

                foreach (var template in dailyTemplates)
                {
                    var existingQuest = await _dbContext.UserQuests
                        .FirstOrDefaultAsync(uq => uq.UserID == userId && 
                                           uq.QuestTemplateID == template.ID && 
                                           uq.CreateAt.Date == today);

                    if (existingQuest == null)
                    {
                        var userQuest = new UserQuest
                        {
                            UserID = userId,
                            QuestTemplateID = template.ID,
                            Current = 0,
                            Status = "active",
                            ExpiresAt = tomorrow,
                            CreateAt = DateTime.UtcNow,
                            UpdateAt = DateTime.UtcNow
                        };

                        _dbContext.UserQuests.Add(userQuest);
                    }
                }

                await _dbContext.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating daily quests for user {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> CreateWeeklyQuestsForUserAsync(int userId)
        {
            try
            {
                var weeklyTemplates = await GetActiveQuestTemplatesAsync(isDaily: false);
                var currentWeek = GetCurrentWeekNumber();
                var nextWeek = DateTime.UtcNow.AddDays(7);

                foreach (var template in weeklyTemplates.Where(t => t.IsWeekly))
                {
                    var existingQuest = await _dbContext.UserQuests
                        .FirstOrDefaultAsync(uq => uq.UserID == userId && 
                                           uq.QuestTemplateID == template.ID && 
                                           uq.WeekNumber == currentWeek);

                    if (existingQuest == null)
                    {
                        var userQuest = new UserQuest
                        {
                            UserID = userId,
                            QuestTemplateID = template.ID,
                            Current = 0,
                            Status = "active",
                            ExpiresAt = nextWeek,
                            WeekNumber = currentWeek,
                            ResetDay = "Monday",
                            CreateAt = DateTime.UtcNow,
                            UpdateAt = DateTime.UtcNow
                        };

                        _dbContext.UserQuests.Add(userQuest);
                    }
                }

                await _dbContext.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating weekly quests for user {UserId}", userId);
                return false;
            }
        }

        private int GetCurrentWeekNumber()
        {
            var today = DateTime.UtcNow;
            var jan1 = new DateTime(today.Year, 1, 1);
            var daysOffset = DayOfWeek.Thursday - jan1.DayOfWeek;
            var firstThursday = jan1.AddDays(daysOffset);
            var cal = System.Globalization.CultureInfo.CurrentCulture.Calendar;
            var firstWeek = cal.GetWeekOfYear(firstThursday, System.Globalization.CalendarWeekRule.FirstFourDayWeek, DayOfWeek.Monday);
            var weekNum = cal.GetWeekOfYear(today, System.Globalization.CalendarWeekRule.FirstFourDayWeek, DayOfWeek.Monday);
            return weekNum;
        }

        public async Task<bool> UpdateQuestProgressAsync(int userId, int questId, int progress)
        {
            try
            {

                var userQuest = await _dbContext.UserQuests
                    .Include(uq => uq.QuestTemplate)
                    .FirstOrDefaultAsync(uq => uq.ID == questId && uq.UserID == userId);

                if (userQuest == null || userQuest.Status != "active")
                    return false;

                userQuest.Current = Math.Min(progress, userQuest.QuestTemplate.Target);
                userQuest.UpdateAt = DateTime.UtcNow;

                // Check if quest is completed
                if (userQuest.Current >= userQuest.QuestTemplate.Target)
                {
                    userQuest.Status = "completed";
                    userQuest.CompletedAt = DateTime.UtcNow;
                }

                // Add progress history
                var progressHistory = new UserQuestProgress
                {
                    UserQuestID = userQuest.ID,
                    Progress = progress,
                    LastUpdated = DateTime.UtcNow
                };
                _dbContext.UserQuestProgresses.Add(progressHistory);

                await _dbContext.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating quest progress for user {UserId}, quest {QuestId}", userId, questId);
                return false;
            }
        }

        public async Task<ClaimRewardResponse> ClaimQuestRewardAsync(int userId, int questId)
        {
            try
            {

                var userQuest = await _dbContext.UserQuests
                    .Include(uq => uq.QuestTemplate)
                    .ThenInclude(qt => qt.Rewards)
                    .FirstOrDefaultAsync(uq => uq.ID == questId && uq.UserID == userId);

                if (userQuest == null)
                    return new ClaimRewardResponse { Success = false, Message = "Quest not found" };

                if (userQuest.Status != "completed")
                    return new ClaimRewardResponse { Success = false, Message = "Quest not completed" };

                var reward = userQuest.QuestTemplate.Rewards.FirstOrDefault();
                if (reward == null)
                    return new ClaimRewardResponse { Success = false, Message = "No reward found" };

                // Give item to user using item system
                if (reward.RewardType == "item" && !string.IsNullOrEmpty(reward.Item))
                {
                    if (int.TryParse(reward.Item, out int itemTemplateId))
                    {
                        var itemResult = await _itemRepository.GiveItemToUserAsync(
                            userId,
                            itemTemplateId,
                            reward.Amount,
                            "quest_reward",
                            userQuest.ID);

                        if (!itemResult.Success)
                            return new ClaimRewardResponse { Success = false, Message = itemResult.Message };
                    }
                }
                else
                {
                    // Legacy reward system for backward compatibility
                    var userReward = new UserRewardInventory
                    {
                        UserID = userId,
                        RewardType = reward.RewardType,
                        Amount = reward.Amount,
                        Item = reward.Item,
                        QuestID = userQuest.ID,
                        EarnedAt = DateTime.UtcNow
                    };
                    _dbContext.UserRewardInventories.Add(userReward);
                }

                // Update user stats
                await UpdateUserStatsAfterReward(userId, reward);

                // Mark quest as claimed
                userQuest.Status = "claimed";
                userQuest.UpdateAt = DateTime.UtcNow;

                await _dbContext.SaveChangesAsync();

                return new ClaimRewardResponse
                {
                    Success = true,
                    Message = "Reward claimed successfully",
                    Reward = new QuestRewardDTO
                    {
                        Type = reward.RewardType,
                        Amount = reward.Amount,
                        Item = reward.Item,
                        Description = reward.Description ?? ""
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error claiming reward for user {UserId}, quest {QuestId}", userId, questId);
                return new ClaimRewardResponse { Success = false, Message = "Error claiming reward" };
            }
        }

        public async Task<List<QuestTemplate>> GetActiveQuestTemplatesAsync(bool isDaily = true)
        {
            try
            {
                return await _dbContext.QuestTemplates
                    .Include(qt => qt.Rewards)
                    .Where(qt => qt.IsActive && qt.IsDaily == isDaily)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active quest templates");
                return new List<QuestTemplate>();
            }
        }

        public async Task<QuestTemplate?> GetQuestTemplateAsync(int templateId)
        {
            try
            {
                return await _dbContext.QuestTemplates
                    .Include(qt => qt.Rewards)
                    .FirstOrDefaultAsync(qt => qt.ID == templateId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting quest template {TemplateId}", templateId);
                return null;
            }
        }

        public async Task<bool> UpdateUserQuestStatsAsync(int userId, UserQuestStats stats)
        {
            try
            {
                var existingStats = await _dbContext.UserQuestStats
                    .FirstOrDefaultAsync(s => s.UserID == userId);

                if (existingStats == null)
                {
                    _dbContext.UserQuestStats.Add(stats);
                }
                else
                {
                    existingStats.TotalCompleted = stats.TotalCompleted;
                    existingStats.StreakDays = stats.StreakDays;
                    existingStats.WeeklyCompleted = stats.WeeklyCompleted;
                    existingStats.MonthlyCompleted = stats.MonthlyCompleted;
                    existingStats.TotalRewardsEarned = stats.TotalRewardsEarned;
                    existingStats.Level = stats.Level;
                    existingStats.Experience = stats.Experience;
                    existingStats.NextLevelExp = stats.NextLevelExp;
                    existingStats.UpdateAt = DateTime.UtcNow;
                }

                await _dbContext.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user quest stats for user {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> InitializeUserQuestStatsAsync(int userId)
        {
            try
            {
                var existingStats = await _dbContext.UserQuestStats
                    .FirstOrDefaultAsync(s => s.UserID == userId);

                if (existingStats == null)
                {
                    var newStats = new UserQuestStats
                    {
                        UserID = userId,
                        TotalCompleted = 0,
                        StreakDays = 0,
                        WeeklyCompleted = 0,
                        MonthlyCompleted = 0,
                        TotalRewardsEarned = 0,
                        Level = 1,
                        Experience = 0,
                        NextLevelExp = 100,
                        LastLoginDate = DateTime.UtcNow.Date,
                        UpdateAt = DateTime.UtcNow
                    };

                    _dbContext.UserQuestStats.Add(newStats);
                    await _dbContext.SaveChangesAsync();
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing user quest stats for user {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> TrackReadChapterAsync(int userId, int chapterId)
        {
            try
            {
                // Find active read_chapters quests for this user
                var readQuests = await _dbContext.UserQuests
                    .Include(uq => uq.QuestTemplate)
                    .Where(uq => uq.UserID == userId &&
                                uq.Status == "active" &&
                                uq.QuestTemplate.QuestType == "read_chapters")
                    .ToListAsync();

                foreach (var quest in readQuests)
                {
                    quest.Current = Math.Min(quest.Current + 1, quest.QuestTemplate.Target);
                    quest.UpdateAt = DateTime.UtcNow;

                    if (quest.Current >= quest.QuestTemplate.Target)
                    {
                        quest.Status = "completed";
                        quest.CompletedAt = DateTime.UtcNow;
                    }
                }

                await _dbContext.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error tracking read chapter for user {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> TrackCommentAsync(int userId, int comicId)
        {
            try
            {
                var commentQuests = await _dbContext.UserQuests
                    .Include(uq => uq.QuestTemplate)
                    .Where(uq => uq.UserID == userId &&
                                uq.Status == "active" &&
                                uq.QuestTemplate.QuestType == "write_comments")
                    .ToListAsync();

                foreach (var quest in commentQuests)
                {
                    quest.Current = Math.Min(quest.Current + 1, quest.QuestTemplate.Target);
                    quest.UpdateAt = DateTime.UtcNow;

                    if (quest.Current >= quest.QuestTemplate.Target)
                    {
                        quest.Status = "completed";
                        quest.CompletedAt = DateTime.UtcNow;
                    }
                }

                await _dbContext.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error tracking comment for user {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> TrackFavoriteAsync(int userId, int comicId)
        {
            try
            {
                var favoriteQuests = await _dbContext.UserQuests
                    .Include(uq => uq.QuestTemplate)
                    .Where(uq => uq.UserID == userId &&
                                uq.Status == "active" &&
                                uq.QuestTemplate.QuestType == "add_favorites")
                    .ToListAsync();

                foreach (var quest in favoriteQuests)
                {
                    quest.Current = Math.Min(quest.Current + 1, quest.QuestTemplate.Target);
                    quest.UpdateAt = DateTime.UtcNow;

                    if (quest.Current >= quest.QuestTemplate.Target)
                    {
                        quest.Status = "completed";
                        quest.CompletedAt = DateTime.UtcNow;
                    }
                }

                await _dbContext.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error tracking favorite for user {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> TrackRatingAsync(int userId, int comicId)
        {
            try
            {
                var ratingQuests = await _dbContext.UserQuests
                    .Include(uq => uq.QuestTemplate)
                    .Where(uq => uq.UserID == userId &&
                                uq.Status == "active" &&
                                uq.QuestTemplate.QuestType == "rate_comics")
                    .ToListAsync();

                foreach (var quest in ratingQuests)
                {
                    quest.Current = Math.Min(quest.Current + 1, quest.QuestTemplate.Target);
                    quest.UpdateAt = DateTime.UtcNow;

                    if (quest.Current >= quest.QuestTemplate.Target)
                    {
                        quest.Status = "completed";
                        quest.CompletedAt = DateTime.UtcNow;
                    }
                }

                await _dbContext.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error tracking rating for user {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> TrackLoginAsync(int userId)
        {
            try
            {
                var loginQuests = await _dbContext.UserQuests
                    .Include(uq => uq.QuestTemplate)
                    .Where(uq => uq.UserID == userId &&
                                uq.Status == "active" &&
                                uq.QuestTemplate.QuestType == "login_streak")
                    .ToListAsync();

                foreach (var quest in loginQuests)
                {
                    quest.Current = Math.Min(quest.Current + 1, quest.QuestTemplate.Target);
                    quest.UpdateAt = DateTime.UtcNow;

                    if (quest.Current >= quest.QuestTemplate.Target)
                    {
                        quest.Status = "completed";
                        quest.CompletedAt = DateTime.UtcNow;
                    }
                }

                // Update user stats for login streak
                await UpdateLoginStreak(userId);

                await _dbContext.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error tracking login for user {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> ExpireOldQuestsAsync()
        {
            try
            {
                var expiredQuests = await _dbContext.UserQuests
                    .Where(uq => uq.Status == "active" && uq.ExpiresAt < DateTime.UtcNow)
                    .ToListAsync();

                foreach (var quest in expiredQuests)
                {
                    quest.Status = "expired";
                    quest.UpdateAt = DateTime.UtcNow;
                }

                await _dbContext.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error expiring old quests");
                return false;
            }
        }

        public async Task<bool> ResetDailyQuestsAsync()
        {
            try
            {
                // This would typically be called by a background service at midnight
                var yesterday = DateTime.UtcNow.Date.AddDays(-1);
                var expiredDailyQuests = await _dbContext.UserQuests
                    .Include(uq => uq.QuestTemplate)
                    .Where(uq => uq.QuestTemplate.IsDaily &&
                                uq.CreateAt.Date == yesterday &&
                                uq.Status == "active")
                    .ToListAsync();

                foreach (var quest in expiredDailyQuests)
                {
                    quest.Status = "expired";
                    quest.UpdateAt = DateTime.UtcNow;
                }

                await _dbContext.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resetting daily quests");
                return false;
            }
        }

        public async Task<bool> ResetWeeklyQuestsAsync()
        {
            try
            {
                var lastWeek = GetCurrentWeekNumber() - 1;
                var expiredWeeklyQuests = await _dbContext.UserQuests
                    .Include(uq => uq.QuestTemplate)
                    .Where(uq => uq.QuestTemplate.IsWeekly &&
                                uq.WeekNumber == lastWeek &&
                                uq.Status == "active")
                    .ToListAsync();

                foreach (var quest in expiredWeeklyQuests)
                {
                    quest.Status = "expired";
                    quest.UpdateAt = DateTime.UtcNow;
                }

                await _dbContext.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resetting weekly quests");
                return false;
            }
        }

        // Helper methods
        private async Task UpdateUserStatsAfterReward(int userId, QuestReward reward)
        {
            var stats = await _dbContext.UserQuestStats
                .FirstOrDefaultAsync(s => s.UserID == userId);

            if (stats == null) return;

            stats.TotalRewardsEarned += reward.Amount;

            if (reward.RewardType == "experience")
            {
                stats.Experience += reward.Amount;

                // Check for level up
                while (stats.Experience >= stats.NextLevelExp)
                {
                    stats.Experience -= stats.NextLevelExp;
                    stats.Level++;
                    stats.NextLevelExp = CalculateNextLevelExp(stats.Level);
                }
            }

            stats.UpdateAt = DateTime.UtcNow;
        }

        private async Task UpdateLoginStreak(int userId)
        {
            var stats = await _dbContext.UserQuestStats
                .FirstOrDefaultAsync(s => s.UserID == userId);

            if (stats == null) return;

            var today = DateTime.UtcNow.Date;
            var yesterday = today.AddDays(-1);

            if (stats.LastLoginDate == yesterday)
            {
                // Continue streak
                stats.StreakDays++;
            }
            else if (stats.LastLoginDate != today)
            {
                // Reset streak if not consecutive
                stats.StreakDays = 1;
            }

            stats.LastLoginDate = today;
            stats.UpdateAt = DateTime.UtcNow;
        }

        private int CalculateNextLevelExp(int level)
        {
            // Simple formula: each level requires 100 more exp than the previous
            return 100 + (level - 1) * 50;
        }

        // Additional methods for controller endpoints
        public async Task<List<QuestTemplateDTO>> GetQuestTemplatesAsync()
        {
            try
            {
                var templates = await _dbContext.QuestTemplates
                    .Include(qt => qt.Rewards)
                    .Where(qt => qt.IsActive)
                    .ToListAsync();

                return templates.Select(qt => new QuestTemplateDTO
                {
                    Id = qt.ID.ToString(),
                    Title = qt.Title,
                    Description = qt.Description ?? "",
                    Type = qt.QuestType,
                    Target = qt.Target,
                    Icon = qt.Icon ?? "",
                    Difficulty = qt.Difficulty,
                    IsDaily = qt.IsDaily,
                    IsWeekly = qt.IsWeekly,
                    IsActive = qt.IsActive,
                    Reward = new QuestRewardDTO
                    {
                        Type = qt.Rewards.FirstOrDefault()?.RewardType ?? "",
                        Amount = qt.Rewards.FirstOrDefault()?.Amount ?? 0,
                        Item = qt.Rewards.FirstOrDefault()?.Item,
                        Description = qt.Rewards.FirstOrDefault()?.Description ?? ""
                    }
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting quest templates");
                return new List<QuestTemplateDTO>();
            }
        }

        public async Task<bool> RefreshDailyQuestsAsync(int userId)
        {
            try
            {
                // Expire old daily quests
                var today = DateTime.UtcNow.Date;
                var oldDailyQuests = await _dbContext.UserQuests
                    .Include(uq => uq.QuestTemplate)
                    .Where(uq => uq.UserID == userId &&
                                uq.QuestTemplate.IsDaily &&
                                uq.CreateAt.Date < today &&
                                uq.Status == "active")
                    .ToListAsync();

                foreach (var quest in oldDailyQuests)
                {
                    quest.Status = "expired";
                    quest.UpdateAt = DateTime.UtcNow;
                }

                // Create new daily quests for today
                await CreateDailyQuestsForUserAsync(userId);

                await _dbContext.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing daily quests for user {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> RefreshWeeklyQuestsAsync(int userId)
        {
            try
            {
                // Expire old weekly quests
                var currentWeek = GetCurrentWeekNumber();
                var oldWeeklyQuests = await _dbContext.UserQuests
                    .Include(uq => uq.QuestTemplate)
                    .Where(uq => uq.UserID == userId &&
                                uq.QuestTemplate.IsWeekly &&
                                uq.WeekNumber < currentWeek &&
                                uq.Status == "active")
                    .ToListAsync();

                foreach (var quest in oldWeeklyQuests)
                {
                    quest.Status = "expired";
                    quest.UpdateAt = DateTime.UtcNow;
                }

                // Create new weekly quests for current week
                await CreateWeeklyQuestsForUserAsync(userId);

                await _dbContext.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing weekly quests for user {UserId}", userId);
                return false;
            }
        }

        public async Task<QuestRefreshStatusDTO> CheckAndRefreshQuestsAsync(int userId)
        {
            try
            {
                var result = new QuestRefreshStatusDTO
                {
                    DailyRefreshed = false,
                    WeeklyRefreshed = false,
                    NextDailyReset = GetNextDailyReset(),
                    NextWeeklyReset = GetNextWeeklyReset()
                };

                var today = DateTime.UtcNow.Date;
                var currentWeek = GetCurrentWeekNumber();

                // Check if daily quests need refresh
                var hasTodayQuests = await _dbContext.UserQuests
                    .Include(uq => uq.QuestTemplate)
                    .AnyAsync(uq => uq.UserID == userId &&
                                   uq.QuestTemplate.IsDaily &&
                                   uq.CreateAt.Date == today);

                if (!hasTodayQuests)
                {
                    result.DailyRefreshed = await RefreshDailyQuestsAsync(userId);
                }

                // Check if weekly quests need refresh
                var hasCurrentWeekQuests = await _dbContext.UserQuests
                    .Include(uq => uq.QuestTemplate)
                    .AnyAsync(uq => uq.UserID == userId &&
                                   uq.QuestTemplate.IsWeekly &&
                                   uq.WeekNumber == currentWeek);

                if (!hasCurrentWeekQuests)
                {
                    result.WeeklyRefreshed = await RefreshWeeklyQuestsAsync(userId);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking and refreshing quests for user {UserId}", userId);
                return new QuestRefreshStatusDTO
                {
                    DailyRefreshed = false,
                    WeeklyRefreshed = false,
                    NextDailyReset = GetNextDailyReset(),
                    NextWeeklyReset = GetNextWeeklyReset()
                };
            }
        }

        public async Task<List<QuestHistoryDTO>> GetQuestHistoryAsync(int userId, int page = 1, int pageSize = 20)
        {
            try
            {
                var skip = (page - 1) * pageSize;
                var completedQuests = await _dbContext.UserQuests
                    .Include(uq => uq.QuestTemplate)
                    .ThenInclude(qt => qt.Rewards)
                    .Where(uq => uq.UserID == userId && uq.Status == "claimed")
                    .OrderByDescending(uq => uq.CompletedAt)
                    .Skip(skip)
                    .Take(pageSize)
                    .ToListAsync();

                return completedQuests.Select(uq => new QuestHistoryDTO
                {
                    Id = uq.ID.ToString(),
                    QuestId = uq.QuestTemplateID.ToString(),
                    QuestTitle = uq.QuestTemplate.Title,
                    CompletedAt = uq.CompletedAt ?? DateTime.MinValue,
                    Difficulty = uq.QuestTemplate.Difficulty,
                    Rewards = uq.QuestTemplate.Rewards.Select(r => new RewardItemDTO
                    {
                        Type = r.RewardType,
                        Amount = r.Amount,
                        Description = r.Description ?? ""
                    }).ToList()
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting quest history for user {UserId}", userId);
                return new List<QuestHistoryDTO>();
            }
        }

        public async Task<List<QuestLeaderboardDTO>> GetQuestLeaderboardAsync(string period = "weekly", int limit = 50)
        {
            try
            {
                var startDate = period.ToLower() switch
                {
                    "daily" => DateTime.UtcNow.Date,
                    "weekly" => DateTime.UtcNow.Date.AddDays(-(int)DateTime.UtcNow.DayOfWeek),
                    "monthly" => new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, 1),
                    _ => DateTime.UtcNow.Date.AddDays(-(int)DateTime.UtcNow.DayOfWeek)
                };

                var leaderboard = await _dbContext.UserQuests
                    .Include(uq => uq.User)
                    .Include(uq => uq.QuestTemplate)
                    .ThenInclude(qt => qt.Rewards)
                    .Where(uq => uq.Status == "claimed" && uq.CompletedAt >= startDate)
                    .GroupBy(uq => uq.UserID)
                    .Select(g => new
                    {
                        UserId = g.Key,
                        Username = g.First().User.FullName,
                        Avatar = g.First().User.Avatar,
                        QuestsCompleted = g.Count(),
                        TotalRewards = g.Sum(uq => uq.QuestTemplate.Rewards.Sum(r => r.Amount))
                    })
                    .OrderByDescending(x => x.QuestsCompleted)
                    .ThenByDescending(x => x.TotalRewards)
                    .Take(limit)
                    .ToListAsync();

                return leaderboard.Select((item, index) => new QuestLeaderboardDTO
                {
                    UserId = item.UserId,
                    Username = item.Username ?? "Unknown User",
                    Avatar = item.Avatar,
                    QuestsCompleted = item.QuestsCompleted,
                    TotalRewards = item.TotalRewards,
                    Rank = index + 1
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting quest leaderboard");
                return new List<QuestLeaderboardDTO>();
            }
        }

        private DateTime GetNextDailyReset()
        {
            var tomorrow = DateTime.UtcNow.Date.AddDays(1);
            return tomorrow;
        }

        private DateTime GetNextWeeklyReset()
        {
            var today = DateTime.UtcNow.Date;
            var daysUntilMonday = ((int)DayOfWeek.Monday - (int)today.DayOfWeek + 7) % 7;
            if (daysUntilMonday == 0) daysUntilMonday = 7; // If today is Monday, next Monday
            return today.AddDays(daysUntilMonday);
        }
    }
}
