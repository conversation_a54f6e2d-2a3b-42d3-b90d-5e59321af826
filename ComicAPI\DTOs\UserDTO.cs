
using ComicApp.Models;

public class UserDTO
{
    public UserDTO() { }
    public UserDTO(User user)
    {
        ID = user.ID;
        Email = user.Email;
        FirstName = user.FirstName;
        LastName = user.LastName;
        Avatar = user.Avatar;
        Dob = user.Dob;
        CreateAt = user.CreateAt;
        Gender = user.Gender;
        Status = user.Status;
        Experience = user.Experience;
        Maxim = user.Maxim;
        TypeLevel = user.TypeLevel;
    }

    public int ID { get; set; }
    public string? Email { get; set; }
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string? Avatar { get; set; }
    public DateTime? Dob { get; set; }
    public DateTime? CreateAt { get; set; }
    public int Gender { get; set; }
    public string? Token { get; set; }
    public int Experience { get; set; } = 0;
    public int? Status { get; set; }
    public string? Maxim { get; set; } = string.Empty;
    public int TypeLevel { get; set; } = 0;
}


public class UserLiteDTO
{
    public UserLiteDTO(User user)
    {
        ID = user.ID;
        FirstName = user.FirstName;
        LastName = user.LastName;
        Avatar = user.Avatar;
        Experience = user.Experience;
        Maxim = user.Maxim;
        TypeLevel = user.TypeLevel;
    }
    public int ID { get; set; }
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string? Avatar { get; set; }
    public int Experience { get; set; } = 0;
    public string? Maxim { get; set; } = string.Empty;
    public int TypeLevel { get; set; } = 0;
}
