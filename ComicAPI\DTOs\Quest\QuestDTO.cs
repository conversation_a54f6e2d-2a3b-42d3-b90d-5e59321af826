namespace ComicApp.DTOs.Quest
{
    public class DailyQuestDTO
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public int Target { get; set; }
        public int Current { get; set; }
        public QuestRewardDTO Reward { get; set; } = new();
        public string Status { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string Difficulty { get; set; } = string.Empty;
        public DateTime ExpiresAt { get; set; }
        public DateTime? CompletedAt { get; set; }
    }

    public class WeeklyQuestDTO : DailyQuestDTO
    {
        public int WeekNumber { get; set; }
        public string ResetDay { get; set; } = string.Empty;
    }

    public class QuestRewardDTO
    {
        public string Type { get; set; } = string.Empty;
        public int Amount { get; set; }
        public string? Item { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    public class QuestProgressDTO
    {
        public string QuestId { get; set; } = string.Empty;
        public int Progress { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    public class UserQuestStatsDTO
    {
        public int TotalCompleted { get; set; }
        public int StreakDays { get; set; }
        public int WeeklyCompleted { get; set; }
        public int MonthlyCompleted { get; set; }
        public int TotalRewardsEarned { get; set; }
        public int Level { get; set; }
        public int Experience { get; set; }
        public int NextLevelExp { get; set; }
    }

    public class ClaimRewardRequest
    {
        public string QuestId { get; set; } = string.Empty;
    }

    public class UpdateQuestProgressRequest
    {
        public string QuestId { get; set; } = string.Empty;
        public int Progress { get; set; }
    }

    public class ClaimRewardResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public QuestRewardDTO? Reward { get; set; }
    }

    public class QuestTemplateDTO
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public int Target { get; set; }
        public string Icon { get; set; } = string.Empty;
        public string Difficulty { get; set; } = string.Empty;
        public bool IsDaily { get; set; }
        public bool IsWeekly { get; set; }
        public bool IsActive { get; set; }
        public QuestRewardDTO Reward { get; set; } = new();
    }

    public class QuestRefreshStatusDTO
    {
        public bool DailyRefreshed { get; set; }
        public bool WeeklyRefreshed { get; set; }
        public DateTime NextDailyReset { get; set; }
        public DateTime NextWeeklyReset { get; set; }
    }

    public class QuestHistoryDTO
    {
        public string Id { get; set; } = string.Empty;
        public string QuestId { get; set; } = string.Empty;
        public string QuestTitle { get; set; } = string.Empty;
        public DateTime CompletedAt { get; set; }
        public string Difficulty { get; set; } = string.Empty;
        public List<RewardItemDTO> Rewards { get; set; } = new();
    }

    public class QuestLeaderboardDTO
    {
        public int UserId { get; set; }
        public string Username { get; set; } = string.Empty;
        public string? Avatar { get; set; }
        public int QuestsCompleted { get; set; }
        public int TotalRewards { get; set; }
        public int Rank { get; set; }
    }

    public class RewardItemDTO
    {
        public string Type { get; set; } = string.Empty;
        public int Amount { get; set; }
        public string? ItemTemplate { get; set; }
        public string Description { get; set; } = string.Empty;
    }
}
