using ComicApp.DTOs.Quest;
using ComicApp.Reposibility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace ComicApp.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class QuestController : ControllerBase
    {
        IUserService _userService;

        private readonly IQuestRepository _questRepository;
        private readonly ILogger<QuestController> _logger;

        public QuestController(IQuestRepository questRepository, ILogger<QuestController> logger, IUserService userService)
        {
            _userService = userService;
            _questRepository = questRepository;
            _logger = logger;
        }

        /// <summary>
        /// Get daily quests for the current user
        /// </summary>
        [HttpGet("daily")]
        public async Task<ActionResult<List<DailyQuestDTO>>> GetDailyQuests()
        {
            try
            {
                var userId = _userService.CurrentUser?.ID;
                if (userId == null)
                    return Unauthorized("User not found");

                var quests = await _questRepository.GetDailyQuestsAsync(userId.Value);
                return Ok(quests);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting daily quests");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get weekly quests for the current user
        /// </summary>
        [HttpGet("weekly")]
        public async Task<ActionResult<List<WeeklyQuestDTO>>> GetWeeklyQuests()
        {
            try
            {
                var userId = _userService.CurrentUser?.ID;
                if (userId == null)
                    return Unauthorized("User not found");

                var quests = await _questRepository.GetWeeklyQuestsAsync(userId.Value);
                return Ok(quests);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting weekly quests");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get user quest statistics
        /// </summary>
        [HttpGet("user-stats")]
        public async Task<ActionResult<UserQuestStatsDTO>> GetUserStats()
        {
            try
            {
                var userId = _userService.CurrentUser?.ID;
                if (userId == null)
                    return Unauthorized("User not found");

                var stats = await _questRepository.GetUserQuestStatsAsync(userId.Value);
                if (stats == null)
                    return NotFound("User stats not found");

                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user stats");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Claim reward for a completed quest
        /// </summary>
        [HttpPost("{questId}/claim")]
        public async Task<ActionResult<ClaimRewardResponse>> ClaimReward(int questId)
        {
            try
            {
                var userId = _userService.CurrentUser?.ID;
                if (userId == null)
                    return Unauthorized("User not found");

                var result = await _questRepository.ClaimQuestRewardAsync(userId.Value, questId);
                
                if (!result.Success)
                    return BadRequest(result);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error claiming reward for quest {QuestId}", questId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Update quest progress manually (for testing purposes)
        /// </summary>
        [HttpPut("{questId}/progress")]
        public async Task<ActionResult<bool>> UpdateQuestProgress(int questId, [FromBody] UpdateQuestProgressRequest request)
        {
            try
            {
                var userId = _userService.CurrentUser?.ID;
                if (userId == null)
                    return Unauthorized("User not found");

                var result = await _questRepository.UpdateQuestProgressAsync(userId.Value, questId, request.Progress);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating quest progress for quest {QuestId}", questId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Initialize quest stats for a user (typically called on first login)
        /// </summary>
        [HttpPost("initialize")]
        public async Task<ActionResult<bool>> InitializeUserQuests()
        {
            try
            {
                int ?userId = _userService.CurrentUser?.ID;
                if (userId == null)
                    return Unauthorized("User not found");

                var result = await _questRepository.InitializeUserQuestStatsAsync(userId.Value);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing user quests");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Refresh daily quests (called automatically at midnight)
        /// </summary>
        [HttpPost("refresh/daily")]
        public async Task<ActionResult<bool>> RefreshDailyQuests()
        {
            try
            {
                var userId = _userService.CurrentUser?.ID;
                if (userId == null)
                    return Unauthorized("User not found");

                var result = await _questRepository.RefreshDailyQuestsAsync(userId.Value);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing daily quests");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Refresh weekly quests (called automatically on Monday)
        /// </summary>
        [HttpPost("refresh/weekly")]
        public async Task<ActionResult<bool>> RefreshWeeklyQuests()
        {
            try
            {
                var userId = _userService.CurrentUser?.ID;
                if (userId == null)
                    return Unauthorized("User not found");

                var result = await _questRepository.RefreshWeeklyQuestsAsync(userId.Value);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing weekly quests");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get all quest templates (for admin purposes)
        /// </summary>
        [HttpGet("templates")]
        public async Task<ActionResult<List<QuestTemplateDTO>>> GetQuestTemplates()
        {
            try
            {
                var templates = await _questRepository.GetQuestTemplatesAsync();
                return Ok(templates);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting quest templates");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Check and auto-refresh quests if needed
        /// </summary>
        [HttpPost("check-refresh")]
        public async Task<ActionResult<QuestRefreshStatusDTO>> CheckAndRefreshQuests()
        {
            try
            {
                var userId = _userService.CurrentUser?.ID;
                if (userId == null)
                    return Unauthorized("User not found");

                var result = await _questRepository.CheckAndRefreshQuestsAsync(userId.Value);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking and refreshing quests");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get quest progress history
        /// </summary>
        [HttpGet("history")]
        public async Task<ActionResult<List<QuestHistoryDTO>>> GetQuestHistory(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                var userId = _userService.CurrentUser?.ID;
                if (userId == null)
                    return Unauthorized("User not found");

                var history = await _questRepository.GetQuestHistoryAsync(userId.Value, page, pageSize);
                return Ok(history);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting quest history");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get leaderboard for quest completion
        /// </summary>
        [HttpGet("leaderboard")]
        public async Task<ActionResult<List<QuestLeaderboardDTO>>> GetQuestLeaderboard(
            [FromQuery] string period = "weekly",
            [FromQuery] int limit = 50)
        {
            try
            {
                var leaderboard = await _questRepository.GetQuestLeaderboardAsync(period, limit);
                return Ok(leaderboard);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting quest leaderboard");
                return StatusCode(500, "Internal server error");
            }
        }

    }
}
